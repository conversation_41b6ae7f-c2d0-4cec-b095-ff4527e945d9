<template>
  <view class="detail-page">
    <view class="detail-content">
      <view class="detail-header">
        <view class="left-content">
          <text class="title">综合评分</text>
          <text class="score">80</text>
        </view>
        <view class="right-content">
          <text class="date">评估日期：2025-09-01</text>
        </view>
      </view>
      <view class="detail-body">
        <view class="body-header">
          <view class="title">详细得分</view>
        </view>
        <view class="body-content">
          <view class="score-table">

          </view>
        </view>
      </view>
    </view>

  </view>
</template>

<script lang="ts">
import {defineComponent} from 'vue'

export default defineComponent({
  name: "detail"
})
</script>

<style scoped lang="scss">
@import '@/static/scss/global.scss';
.detail-page {
  @include globalPageStyle();
}
.detail-content {
  padding: 20rpx;
  .detail-header {
    height: 168rpx;
    border-radius: 16rpx;
    background: rgba(255, 255, 255, 0.90);
    box-shadow: 0 4rpx 16rpx 0 rgba(0, 0, 0, 0.12);
    display: flex;
    justify-content: space-between;
    padding: 20rpx;
    .left-content {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .title {
        font-size: 28rpx;
        font-weight: 600;
        color: #2F3133;
        font-family: "PingFang SC";
        margin-left: 6rpx;
      }
      .score {
        font-family: "PingFang SC";
        font-size: 96rpx;
        font-weight: 600;
        color: #303030;
        background:  linear-gradient(153deg, #7EADFD 11.75%, #4068F5 75.43%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }
    .right-content {
      display: flex;
      align-items: center;
      .date {
        font-family: "PingFang SC";
        font-size: 24rpx;
        font-weight: 400;
        color: #8D9094;
      }
    }
  }
  .detail-body {
    padding: 20rpx;
    border-radius: 16rpx;
    background: rgba(255, 255, 255, 0.90);
    margin-top: 20rpx;
    /* 下层投影 */
    box-shadow: 0 4rpx 16rpx 0 rgba(0, 0, 0, 0.12);
    .body-header {
      .title {
        font-family: "PingFang SC";
        font-size: 28rpx;
        font-weight: 600;
        color: #2F3133;
        &::before {
          background: url("./img/detail-icon.png") no-repeat;
          content: "";
          display: inline-block;
          width: 34rpx;
          height: 34rpx;
          margin-right: 6rpx;
          vertical-align: middle;
          background-size: contain;
        }
      }
    }
  }
}


</style>